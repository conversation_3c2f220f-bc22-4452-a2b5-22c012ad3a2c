import { z } from "zod";

export const subscriptionFormSchema = z.object({
  email: z.string().email("Invalid email address"),
});

export const quickSearchBarSchema = z.object({
  search: z.string().min(1, "Search term is required"),
});

export const contactUsFormSchema = z.object({
  name: z.string().min(1, "Name is required"),
  email: z.string().email("Invalid email address"),
  phone: z.string().min(1, "Phone number is required"),
  subject: z.string().min(1, "Subject is required"),
  message: z.string().min(30, "Message is required, minimum 30 characters"),
});

export const loginFormSchema = z.object({
  email: z.string().email("Invalid email address"),
  password: z.string().min(8, "Password is required, minimum 8 characters"),
  rememberMe: z.boolean().optional(),
});

export type SubscriptionFormValues = z.infer<typeof subscriptionFormSchema>;
export type QuickSearchBarValues = z.infer<typeof quickSearchBarSchema>;
export type ContactUsFormValues = z.infer<typeof contactUsFormSchema>;
export type LoginFormValues = z.infer<typeof loginFormSchema>;
