"use client";

import React, { useEffect } from "react";
import { useSignupStore } from "@/store/userSignUpStore";
import { getSteps } from "@/lib/getOnboardingSteps";
import { useRouter } from "next/navigation";
import Link from "next/link";

interface OnboardingLayoutProps {
  children: React.ReactNode;
}

const OnboardingLayout = ({ children }: OnboardingLayoutProps) => {
  const router = useRouter();
  const { data, steps, setSteps, currentStep } = useSignupStore();

  useEffect(() => {
    if (!data.intent || !data.spaceType) {
      router.push("/auth/register");
    }
  }, [data.intent, data.spaceType]);

  useEffect(() => {
    if (!steps.length && data.intent && data.spaceType) {
      const generatedSteps = getSteps(data.intent, data.spaceType);
      setSteps(generatedSteps);
    }
  }, [data.intent, data.spaceType]);

  console.log(currentStep);

  return (
    <section>
      <div className="container">
        <div className="grid grid-cols-12 gap-5">
          <div className="col-span-12 lg:col-span-4">
            <aside className="w-64 border-r p-4 space-y-4">
              {steps.map((step, index) => (
                <Link href={`/auth/onboarding/${index}`} key={index} className="block p-2">
                  {index + 1} {step}
                </Link>
              ))}
            </aside>
          </div>
          <div className="col-span-12 lg:col-span-8">{children}</div>
        </div>
      </div>
    </section>
  );
};

export default OnboardingLayout;
