"use client";

import React, { useEffect } from "react";
import { useSignupStore } from "@/store/userSignUpStore";
import { useParams, useRouter } from "next/navigation";
import {
  ABOUT_YOU,
  RENTAL_BASICS,
  RENTAL_PREFERENCES,
  ROOMMATE_PREFERENCES,
  TENANT_PREFERENCES,
  THE_HOME,
  THE_ROOM,
  THE_SPACE,
  UPLOAD_PROFILE_PHOTO,
  UPLOAD_SPACE_PHOTOS,
} from "@/constants/auth.constants";
import AboutYouStep from "./_components/AboutYouStep";
import ProfilePhotoStep from "./_components/ProfilePhotoStep";
import HomeStep from "./_components/HomeStep";
import RoomStep from "./_components/RoomStep";
import SpaceStep from "./_components/SpaceStep";
import SpacePhotosStep from "./_components/SpacePhotosStep";
import RoommatePreferencesStep from "./_components/RoommatePreferencesStep";
import RentalBasicsStep from "./_components/RentalBasicsStep";
import TenantPreferencesStep from "./_components/TenantPreferencesStep";
import RentalPreferencesStep from "./_components/RentalPreferencesStep";
import { Button } from "@/components/ui/button";

const OnboardingPage = () => {
  const { step } = useParams();
  const router = useRouter();
  const currentStep = parseInt(step as string);
  const { data, steps, setCurrentStep } = useSignupStore();

  useEffect(() => {
    if (!steps.length) {
      router.push("/auth/register");
    } else {
      setCurrentStep(currentStep);
    }
  }, [currentStep]);

  const stepName = steps[currentStep];

  console.log(data);

  // Map step names to components
  const renderStepForm = () => {
    switch (stepName) {
      case ABOUT_YOU:
        return <AboutYouStep />;
      case UPLOAD_PROFILE_PHOTO:
        return <ProfilePhotoStep />;
      case THE_HOME:
        return <HomeStep />;
      case THE_ROOM:
        return <RoomStep />;
      case THE_SPACE:
        return <SpaceStep />;
      case UPLOAD_SPACE_PHOTOS:
        return <SpacePhotosStep />;
      case ROOMMATE_PREFERENCES:
        return <RoommatePreferencesStep />;
      case RENTAL_BASICS:
        return <RentalBasicsStep />;
      case TENANT_PREFERENCES:
        return <TenantPreferencesStep />;
      case RENTAL_PREFERENCES:
        return <RentalPreferencesStep />;
      default:
        return <div>Form not found for step: {stepName}</div>;
    }
  };

  const handlePrevious = () => {
    if (currentStep === 0) return;
    router.push(`/auth/onboarding/${currentStep - 1}`);
  };

  const handleNext = () => {
    if (currentStep === steps.length - 1) return;
    router.push(`/auth/onboarding/${currentStep + 1}`);
  };

  return (
    <div>
      <h2 className="text-2xl font-semibold text-secondary">{stepName}</h2>
      {renderStepForm()}
      <div className="flex justify-between mt-8">
        <Button
          type="button"
          variant="outline"
          onClick={handlePrevious}
          disabled={currentStep === 0}
        >
          Previous
        </Button>
        <Button type="button" onClick={handleNext} disabled={currentStep === steps.length - 1}>
          {currentStep === steps.length - 1 ? "Complete" : "Next"}
        </Button>
      </div>
    </div>
  );
};

export default OnboardingPage;
