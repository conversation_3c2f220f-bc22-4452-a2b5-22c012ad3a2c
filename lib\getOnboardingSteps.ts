// lib/getOnboardingSteps.ts
export function getSteps(intent: string, spaceType: string): string[] {
  if (intent === "rent" && spaceType === "private") {
    return [
      "About You",
      "Upload Profile Photo",
      "The Home",
      "The Room",
      "Upload Space Photos",
      "Roommate Preferences",
    ];
  }

  if (intent === "rent" && spaceType === "entire") {
    return [
      "About You",
      "Upload Profile Photo",
      "The Space",
      "Upload Space Photos",
      "Rental Basics",
      "Tenant Preferences",
    ];
  }

  if (intent === "find" && spaceType === "private") {
    return ["About You", "Upload Profile Photo", "The Room", "Roommate Preferences"];
  }

  if (intent === "find" && spaceType === "entire") {
    return ["About You", "Upload Profile Photo", "Rental Preferences"];
  }

  return [];
}
