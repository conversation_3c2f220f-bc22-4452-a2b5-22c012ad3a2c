import { z } from "zod";

export const registerSchema = z.object({
  firstName: z.string().min(2),
  lastName: z.string().min(2),
  email: z.string().email(),
  intent: z.string().min(1),
  spaceType: z.string().min(1),
});

export const aboutYouSchema = z.object({
  genderIdentity: z.string().min(1),
  sexualOrientation: z.string().min(1),
  age: z.number().min(10).optional(),
  smokeCigarettes: z.string().optional(),
  smokeMarijuana: z.string().optional(),
  workFromHome: z.string().optional(),
  travel: z.string().optional(),
  cleanliness: z.string().min(1).optional(),
  describeMyselfAs: z.string().min(1),
  zodiac: z.string().optional(),
  selfDescription: z.string().min(1),
  fluentLanguages: z.array(z.string()).optional(),
});

export const homeSchema = z.object({
  fullAddress: z.string().min(1),
  residenceType: z.string().min(1),
  size: z.number().min(1),
  bedrooms: z.number().min(0),
  bathrooms: z.number().min(0),
  ownerOccupied: z.boolean().optional(),
  numPeopleInHome: z.number().optional(),
  amenities: z.array(z.string()).optional(),
  parking: z.string().optional(),
  neighborhood: z.string().optional(),
  currentPets: z.array(z.string()).optional(),
  allowedPets: z.array(z.string()).optional(),
  cigaretteSmokingAllowed: z.boolean().optional(),
  marijuanaSmokingAllowed: z.boolean().optional(),
  overnightGuestsAllowed: z.boolean().optional(),
  homeDescription: z.string().min(1),
});

export const roomSchema = z.object({
  availabilityDate: z.string(),
  availabilityDuration: z.string(),
  minimumDuration: z.string(),
  monthlyRent: z.number(),
  depositAmount: z.number(),
  leaseRequired: z.boolean(),
  requiredReferences: z.boolean(),
  utilitiesIncluded: z.boolean(),
  furnished: z.boolean(),
  bedroomSize: z.string(),
  brightness: z.string(),
  bathroom: z.string(),
  roomFeatures: z.array(z.string()),
});

export const roommatePrefsSchema = z.object({
  preferredGenderIdentity: z.string(),
  preferredSexualOrientation: z.string(),
  preferredAgeRange: z.string(),
  preferredSmokingHabits: z.string(),
  idealRoommateDescription: z.string(),
});

export const rentalPrefsSchema = z.object({
  preferredLocation: z.string(),
  rentalStartDate: z.string(),
  rentalDuration: z.string().optional(),
  maxMonthlyBudget: z.number(),
  willingToSignRentalAgreement: z.boolean(),
  wantFurnished: z.boolean(),
  bedrooms: z.number().optional(),
  bathrooms: z.number().optional(),
  pets: z.string().optional(),
  parkingRequired: z.boolean().optional(),
});

export type SignupData = z.infer<typeof registerSchema>;
export type AboutYouData = z.infer<typeof aboutYouSchema>;
export type HomeData = z.infer<typeof homeSchema>;
export type RoomData = z.infer<typeof roomSchema>;
export type RoommatePrefsData = z.infer<typeof roommatePrefsSchema>;
export type RentalPrefsData = z.infer<typeof rentalPrefsSchema>;
